import React, { useState, useEffect, useMemo } from 'react';
import { Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { ToolPanelProps, ToolCategory, ToolItem } from './data.d';
import './styles.less';

// 默认工具数据
const defaultCategories: ToolCategory[] = [
  {
    id: 'ai',
    name: '大模型',
    tools: [
      { id: 'ai-model', name: '大模型', icon: '🤖', color: 'ai-model' },
    ],
  },
  {
    id: 'workflow',
    name: '工作流',
    tools: [
      { id: 'workflow', name: '工作流', icon: '⚡', color: 'workflow' },
      { id: 'plugin', name: '插件', icon: '🧩', color: 'workflow' },
    ],
  },
  {
    id: 'business',
    name: '业务逻辑',
    tools: [
      { id: 'code', name: '代码', icon: '</>', color: 'code' },
      { id: 'intent', name: '意图识别', icon: '🎯', color: 'business' },
      { id: 'batch', name: '批处理', icon: '📦', color: 'batch' },
      { id: 'selector', name: '选择器', icon: 'IF', color: 'selector' },
      { id: 'loop', name: '循环', icon: '🔄', color: 'loop' },
      { id: 'variable', name: '变量聚合', icon: '📊', color: 'variable' },
    ],
  },
  {
    id: 'io',
    name: '输入&输出',
    tools: [
      { id: 'input', name: '输入', icon: '📥', color: 'input' },
      { id: 'output', name: '输出', icon: '📤', color: 'output' },
    ],
  },
  {
    id: 'database',
    name: '数据库',
    tools: [
      { id: 'sql-custom', name: 'SQL自定义', icon: '🗃️', color: 'database' },
      { id: 'data-add', name: '新增数据', icon: '➕', color: 'database' },
      { id: 'data-update', name: '更新数据', icon: '✏️', color: 'database' },
      { id: 'data-query', name: '查询数据', icon: '🔍', color: 'database' },
      { id: 'data-delete', name: '删除数据', icon: '🗑️', color: 'database' },
    ],
  },
  {
    id: 'knowledge',
    name: '知识库&数据',
    tools: [
      { id: 'knowledge-write', name: '知识库写入', icon: '📝', color: 'knowledge' },
      { id: 'knowledge-search', name: '知识库检索', icon: '🔎', color: 'knowledge' },
      { id: 'knowledge-delete', name: '知识库删除', icon: '🗂️', color: 'knowledge' },
      { id: 'long-memory', name: '长期记忆', icon: '🧠', color: 'knowledge' },
    ],
  },
];

/**
 * 工具面板组件
 */
const ToolPanel: React.FC<ToolPanelProps> = ({
  categories = defaultCategories,
  onToolClick,
  searchPlaceholder = '搜索节点、插件、工作流',
  showSearch = true,
  className = '',
  style = {},
}) => {
  const [searchValue, setSearchValue] = useState<string>('');

  // 过滤工具数据
  const filteredCategories = useMemo(() => {
    if (!searchValue.trim()) {
      return categories;
    }

    const filtered: ToolCategory[] = [];
    categories.forEach((category) => {
      const filteredTools = category.tools.filter((tool) =>
        tool.name.toLowerCase().includes(searchValue.toLowerCase()) ||
        (tool.description && tool.description.toLowerCase().includes(searchValue.toLowerCase()))
      );

      if (filteredTools.length > 0) {
        filtered.push({
          ...category,
          tools: filteredTools,
        });
      }
    });

    return filtered;
  }, [categories, searchValue]);

  // 处理工具点击
  const handleToolClick = (tool: ToolItem) => {
    if (tool.onClick) {
      tool.onClick();
    }
    if (onToolClick) {
      onToolClick(tool);
    }
  };

  // 渲染工具项
  const renderToolItem = (tool: ToolItem) => (
    <div
      key={tool.id}
      className="tool-item"
      onClick={() => handleToolClick(tool)}
      title={tool.description || tool.name}
    >
      <div className={`tool-icon ${tool.color}`}>
        {tool.icon}
      </div>
      <div className="tool-name">{tool.name}</div>
    </div>
  );

  // 渲染分类
  const renderCategory = (category: ToolCategory) => (
    <div key={category.id} className="category-section">
      <div className="category-title">{category.name}</div>
      <div className="tools-grid">
        {category.tools.map(renderToolItem)}
      </div>
    </div>
  );

  return (
    <div className={`tool-panel ${className}`} style={style}>
      {showSearch && (
        <div className="search-container">
          <Input
            className="search-input"
            placeholder={searchPlaceholder}
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            allowClear
          />
        </div>
      )}

      <div className="categories-container">
        {filteredCategories.length > 0 ? (
          filteredCategories.map(renderCategory)
        ) : (
          <div className="no-results">
            没有找到匹配的工具
          </div>
        )}
      </div>
    </div>
  );
};

export default ToolPanel;
